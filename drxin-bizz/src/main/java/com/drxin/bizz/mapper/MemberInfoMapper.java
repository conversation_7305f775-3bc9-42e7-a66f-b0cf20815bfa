package com.drxin.bizz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.drxin.bizz.domain.MemberInfo;
import com.drxin.bizz.vo.MemberAiderStatVo;
import com.drxin.bizz.vo.MemberAiderStatExportVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MemberInfoMapper extends BaseMapper<MemberInfo> {

    List<MemberInfo> selectMemberInfoList(MemberInfo memberInfo);

    MemberInfo selectMemberInfoById(Long id);

    /**
     * 检查成交人循环
     *
     * @param userIds 要更新的用户ID列表
     * @param newDealInviterId 新的成交人ID
     * @return 如果会形成循环返回大于0的数，否则返回0
     */
    int checkDealInviterCycle(@Param("userIds") List<Long> userIds, @Param("newDealInviterId") Long newDealInviterId);

    /**
     * 检查邀请人循环
     *
     * @param userIds 要更新的用户ID列表
     * @param newInviterId 新的邀请人ID
     * @return 如果会形成循环返回大于0的数，否则返回0
     */
    int checkInviterCycle(@Param("userIds") List<Long> userIds, @Param("newInviterId") Long newInviterId);

    /**
     * 查询成员急救员统计列表
     *
     * @param memberAiderStatVo 查询条件
     * @return 成员急救员统计列表
     */
    List<MemberAiderStatVo> selectMemberAiderStatList(MemberAiderStatVo memberAiderStatVo);

    /**
     * 查询成员急救员统计导出列表
     *
     * @param memberAiderStatVo 查询条件
     * @return 成员急救员统计导出列表
     */
    List<MemberAiderStatExportVo> selectMemberAiderStatExportList(MemberAiderStatVo memberAiderStatVo);
}
