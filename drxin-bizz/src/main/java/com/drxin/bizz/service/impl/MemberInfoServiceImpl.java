package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.bizz.constants.UserTypeRoleMapConstants;
import com.drxin.bizz.domain.MemberInfo;
import com.drxin.bizz.mapper.MemberInfoMapper;
import com.drxin.bizz.service.IMemberInfoService;
import com.drxin.bizz.vo.MemberUpdateTypeVo;
import com.drxin.bizz.vo.MemberAiderStatVo;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.common.utils.StringUtils;
import com.drxin.system.domain.SysUserRole;
import com.drxin.system.mapper.SysUserMapper;
import com.drxin.system.mapper.SysUserRoleMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MemberInfoServiceImpl extends ServiceImpl<MemberInfoMapper, MemberInfo> implements IMemberInfoService {
    @Resource
    private SysUserRoleMapper userRoleMapper;

    @Resource
    private SysUserMapper userMapper;

    @Override
    public List<MemberInfo> selectMemberInfoList(MemberInfo memberInfo) {
        return baseMapper.selectMemberInfoList(memberInfo);
    }

    @Override
    public MemberInfo selectMemberInfoById(Long id) {
        return baseMapper.selectMemberInfoById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMemberType(MemberUpdateTypeVo memberUpdateTypeVo) {
        // 获取修改用户的id
        List<Long> ids = memberUpdateTypeVo.getIds();
        // 判断成交人是否是自己
        if (ids.contains(memberUpdateTypeVo.getDealInviterId())) {
            throw new ServiceException("成交人不能是自己");
        }

//        // 检查成交人循环
//        if (baseMapper.checkDealInviterCycle(ids, memberUpdateTypeVo.getDealInviterId()) > 0) {
//            throw new ServiceException("设置成交人会形成循环引用，请检查成交人关系");
//        }

        // 获取修改后的用户类型（支持多个类型，逗号分割）
        String userTypes = memberUpdateTypeVo.getUpdateUserType();
        if (StringUtils.isBlank(userTypes)) {
            throw new ServiceException("用户类型不能为空");
        }

        // 解析用户类型
        List<String> typeList = Arrays.stream(userTypes.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (typeList.isEmpty()) {
            throw new ServiceException("用户类型不能为空");
        }

        // 删除用户原有的角色
        userRoleMapper.deleteUserRole(ids.toArray(new Long[0]));

        // 获取所有类型对应的角色ID
        Set<Integer> roleIds = new HashSet<>();
        for (String type : typeList) {
            try {
                UserTypeRoleMapConstants userTypeRoleMapConstants = UserTypeRoleMapConstants.valueOf(type.toUpperCase());
                roleIds.add(userTypeRoleMapConstants.getRoleId());
            } catch (IllegalArgumentException e) {
                throw new ServiceException("不支持的用户类型: " + type);
            }
        }

        // 更改用户的用户类型（存储为逗号分割的字符串）
        UpdateWrapper<SysUser> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("user_id", ids);
        updateWrapper.set("user_type", userTypes); // 直接存储完整的类型字符串
        updateWrapper.set("deal_inviter_id", memberUpdateTypeVo.getDealInviterId());
        userMapper.update(null, updateWrapper);

        // 批量插入用户角色关系
        List<SysUserRole> sysUserRoleList = new ArrayList<>();
        for (Long userId : ids) {
            for (Integer roleId : roleIds) {
                sysUserRoleList.add(new SysUserRole(userId, roleId));
            }
        }

        if (!sysUserRoleList.isEmpty()) {
            userRoleMapper.batchUserRole(sysUserRoleList);
        }

        return 1;
    }

    @Override
    public List<MemberInfo> selectMemberInfoListToSelect(MemberUpdateTypeVo memberUpdateTypeVo) {
        String keyword = memberUpdateTypeVo.getKeyWord();
        QueryWrapper<MemberInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("user_id", "real_name", "id_card", "phonenumber");
        if (memberUpdateTypeVo.getUpdateUserType() != null) {
            List<String> typeList = Arrays.stream(memberUpdateTypeVo.getUpdateUserType().split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (!typeList.isEmpty()) {
                queryWrapper.in("user_type", typeList);
            }
        }
        queryWrapper.isNotNull("user_type");
        queryWrapper.isNotNull("real_name");
        if (StringUtils.isNotEmpty(keyword)) {
            queryWrapper.and(wrapper -> wrapper.like("real_name", keyword)
                    .or()
                    .eq("id_card", keyword).or().eq("nick_name", keyword));
        }
        List<MemberInfo> memberInfos = baseMapper.selectList(queryWrapper);

        // 对身份证和手机号进行脱敏处理
        for (MemberInfo memberInfo : memberInfos) {
            // 身份证脱敏
            if (StringUtils.isNotEmpty(memberInfo.getIdCard())) {
                String idCard = memberInfo.getIdCard();
                if (idCard.length() >= 8) {
                    // 长度>=8：保留前4位和后4位
                    memberInfo.setIdCard(StringUtils.hide(idCard, 4, idCard.length() - 4));
                } else if (idCard.length() > 4) {
                    // 长度>4但<8：保留前2位和后2位
                    memberInfo.setIdCard(StringUtils.hide(idCard, 2, idCard.length() - 2));
                }
                // 长度<=4：不脱敏
            }

            // 手机号脱敏
            if (StringUtils.isNotEmpty(memberInfo.getPhoneNumber())) {
                String phone = memberInfo.getPhoneNumber();
                if (phone.length() == 11) {
                    // 标准11位手机号：保留前3位和后4位
                    memberInfo.setPhoneNumber(StringUtils.hide(phone, 3, 7));
                } else if (phone.length() > 7) {
                    // 其他长度>7：保留前3位和后4位
                    memberInfo.setPhoneNumber(StringUtils.hide(phone, 3, phone.length() - 4));
                } else if (phone.length() > 4) {
                    // 长度>4但<=7：保留前2位和后2位
                    memberInfo.setPhoneNumber(StringUtils.hide(phone, 2, phone.length() - 2));
                }
                // 长度<=4：不脱敏
            }
        }
        return memberInfos;
    }

    /**
     * 更新成员成交人
     * 注意：方法名为updateMemberInviter但实际更新的是成交人(deal_inviter_id)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMemberInviter(String ids, String newInviterId) {
        if (StringUtils.isBlank(ids)) {
            throw new ServiceException("用户ID不能为空");
        }
        if (StringUtils.isBlank(newInviterId)) {
            throw new ServiceException("新成交人ID不能为空");
        }

        // 解析用户ID列表
        List<Long> userIds = Arrays.stream(ids.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toList());

        if (userIds.isEmpty()) {
            throw new ServiceException("用户ID不能为空");
        }

        // 检查新成交人是否存在
        SysUser newDealInviter = userMapper.selectById(Long.valueOf(newInviterId));
        if (newDealInviter == null) {
            throw new ServiceException("新成交人不存在");
        }

        // 检查是否包含新成交人自己
        if (userIds.contains(Long.valueOf(newInviterId))) {
            throw new ServiceException("不能将用户设置为自己的成交人");
        }

        int count = baseMapper.checkDealInviterCycle(userIds, Long.valueOf(newInviterId));
        // 检查成交人循环
        if ( count > 0) {
            throw new ServiceException("设置成交人会形成循环引用，请检查成交人关系");
        }

        // 批量更新成交人
        UpdateWrapper<SysUser> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("user_id", userIds);
        updateWrapper.set("deal_inviter_id", newInviterId);

        return userMapper.update(null, updateWrapper);
    }

    @Override
    public List<MemberInfo> getRecommendList(MemberInfo memberInfo) {
        QueryWrapper<MemberInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deal_inviter_id", memberInfo.getUserId());
        if (memberInfo.getRealName() != null) {
            queryWrapper.like("real_name", memberInfo.getRealName());
        }
        queryWrapper.select("real_name", "user_type", "upgraded_time");
        queryWrapper.orderByDesc("upgraded_time");
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<MemberAiderStatVo> selectMemberAiderStatList(MemberAiderStatVo memberAiderStatVo) {
        return baseMapper.selectMemberAiderStatList(memberAiderStatVo);
    }

}
