package com.drxin.bizz.controller;

import com.drxin.bizz.domain.MemberInfo;
import com.drxin.bizz.service.IMemberInfoService;
import com.drxin.bizz.vo.MemberUpdateTypeVo;
import com.drxin.bizz.vo.MemberAiderStatVo;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.core.page.TableDataInfo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/bizz/member_info")
public class MemberInfoController extends BaseController {

    @Resource
    private IMemberInfoService memberInfoService;

    @GetMapping("/list")
    public TableDataInfo list(MemberInfo memberInfo) {
        startPage();
        List<MemberInfo> memberInfos = memberInfoService.selectMemberInfoList(memberInfo);
        return getDataTable(memberInfos);
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(memberInfoService.selectMemberInfoById(id));
    }

    @PutMapping("/updateMemberType")
    public AjaxResult updateMemberType(@RequestBody MemberUpdateTypeVo memberUpdateTypeVo) {
        return toAjax(memberInfoService.updateMemberType(memberUpdateTypeVo));
    }

    @GetMapping("/selectMemberList")
    public TableDataInfo selectMemberInfoListToSelect(MemberUpdateTypeVo memberUpdateTypeVo) {
        startPage();
        List<MemberInfo> memberInfos = memberInfoService.selectMemberInfoListToSelect(memberUpdateTypeVo);
        return getDataTable(memberInfos);
    }

    @PutMapping("/updateMemberInviter/{ids}/{newInviterId}")
    public AjaxResult updateMemberInviter(@PathVariable("ids") String ids, @PathVariable("newInviterId") String newInviterId) {
        return toAjax(memberInfoService.updateMemberInviter(ids, newInviterId));
    }

    @GetMapping("/getRecommendList")
    public AjaxResult getRecommendList(MemberInfo memberInfo) {
        startPage();
        return AjaxResult.success(getDataTable(memberInfoService.getRecommendList(memberInfo)));
    }

    @GetMapping("/aiderStat")
    public TableDataInfo getAiderStatList(MemberAiderStatVo memberAiderStatVo) {
        startPage();
        List<MemberAiderStatVo> list = memberInfoService.selectMemberAiderStatList(memberAiderStatVo);
        return getDataTable(list);
    }
}
